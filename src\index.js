// Cloudflare Workers 白名单订阅网关
// 支持IP白名单和订阅地址管理

export default {
  async fetch(request, env, ctx) {
    try {
      const url = new URL(request.url);
      const path = url.pathname;

      // 获取客户端IP
      const clientIP = request.headers.get('CF-Connecting-IP') ||
                      request.headers.get('X-Forwarded-For') ||
                      request.headers.get('X-Real-IP') ||
                      '127.0.0.1';

      // 路由处理
      if (path === '/' || path === '/admin') {
        return handleHome(request, env);
      } else if (path.startsWith('/api/')) {
        return handleAPI(request, env, clientIP);
      } else if (path.startsWith('/proxy/')) {
        return handleProxy(request, env, clientIP);
      } else if (path.startsWith('/status/')) {
        return handleStatus(request, env, clientIP);
      } else if (path === '/whitelist-helper') {
        return handleWhitelistHelper(request);
      }

      return new Response('页面未找到', { status: 404 });
    } catch (error) {
      console.error('全局错误处理:', error);

      // 如果是API请求，返回JSON错误
      const url = new URL(request.url);
      if (url.pathname.startsWith('/api/') || request.method === 'POST') {
        return new Response(JSON.stringify({
          success: false,
          error: `服务器内部错误: ${error.message}`
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 否则返回HTML错误页面
      return new Response(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>服务器错误</title>
          <meta charset="utf-8">
        </head>
        <body>
          <h1>服务器内部错误</h1>
          <p>抱歉，服务器遇到了一个错误。请稍后重试。</p>
          <p>错误详情：${error.message}</p>
        </body>
        </html>
      `, {
        status: 500,
        headers: { 'Content-Type': 'text/html' }
      });
    }
  }
};

// 处理首页
async function handleHome(request, env) {
  if (request.method === 'GET') {
    return new Response(getHomeHTML(), {
      headers: { 'Content-Type': 'text/html' }
    });
  }

  if (request.method === 'POST') {
    try {
      const formData = await request.formData();
      const action = formData.get('action');

    if (action === 'create_proxy') {
      const originalUrl = formData.get('original_url');

      if (!originalUrl) {
        return new Response(JSON.stringify({
          success: false,
          error: '订阅地址不能为空'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 检查域名限制
      if (env.ALLOWED_DOMAINS) {
        try {
          const urlObj = new URL(originalUrl);
          const allowedDomains = env.ALLOWED_DOMAINS.split(',').map(d => d.trim());
          const isAllowed = allowedDomains.some(domain =>
            urlObj.hostname === domain || urlObj.hostname.endsWith('.' + domain)
          );

          if (!isAllowed) {
            return new Response(JSON.stringify({
              success: false,
              error: '订阅地址域名不在允许列表中'
            }), {
              headers: { 'Content-Type': 'application/json' }
            });
          }
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            error: '订阅地址格式无效'
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        }
      }

      // 检查是否已存在相同的订阅地址
      const existingProxy = await env.DB.prepare(`
        SELECT id FROM proxies WHERE subscription_url = ?
      `).bind(originalUrl).first();

      if (existingProxy) {
        return new Response(JSON.stringify({
          success: false,
          error: '该订阅地址已存在网关',
          existing_proxy_id: existingProxy.id,
          message: '如需管理现有网关，请使用网关ID和管理密码登录'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      const proxyId = generateId();
      const proxyPassword = generatePassword();

      // 存储代理信息到D1数据库
      await env.DB.prepare(`
        INSERT INTO proxies (id, subscription_url, password)
        VALUES (?, ?, ?)
      `).bind(proxyId, originalUrl, proxyPassword).run();

      const proxyUrl = `${new URL(request.url).origin}/proxy/${proxyId}`;
      const whitelistUrl = `${new URL(request.url).origin}/api/whitelist/${proxyId}?action=add`;
      const checkUrl = `${new URL(request.url).origin}/api/whitelist/${proxyId}`;
      const statusUrl = `${new URL(request.url).origin}/status/${proxyId}`;

      return new Response(JSON.stringify({
        success: true,
        proxy_url: proxyUrl,
        whitelist_url: whitelistUrl,
        check_url: checkUrl,
        status_url: statusUrl,
        proxy_id: proxyId,
        proxy_password: proxyPassword
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (action === 'manage_proxy') {
      const proxyIdOrUrl = formData.get('proxy_id');
      const proxyPassword = formData.get('proxy_password');
      const subAction = formData.get('sub_action');

      if (!proxyIdOrUrl || !proxyPassword) {
        return new Response(JSON.stringify({
          success: false,
          error: '网关ID/订阅地址和密码不能为空'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 判断输入的是网关ID还是订阅地址
      let proxyId = proxyIdOrUrl;
      let proxyData;

      if (proxyIdOrUrl.startsWith('http://') || proxyIdOrUrl.startsWith('https://')) {
        // 如果是订阅地址，查找对应的代理
        proxyData = await env.DB.prepare(`
          SELECT id, password FROM proxies WHERE subscription_url = ?
        `).bind(proxyIdOrUrl).first();

        if (!proxyData) {
          return new Response(JSON.stringify({
            success: false,
            error: '未找到该订阅地址对应的代理'
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        }
        proxyId = proxyData.id;
      } else {
        // 如果是网关ID，直接查找
        proxyData = await env.DB.prepare(`
          SELECT id, password FROM proxies WHERE id = ?
        `).bind(proxyId).first();
      }

      // 验证代理密码
      if (!proxyData || proxyData.password !== proxyPassword) {
        return new Response(JSON.stringify({
          success: false,
          error: '网关ID/订阅地址或密码错误'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      if (subAction === 'add_ip') {
        const ip = formData.get('ip_address');

        if (!ip) {
          return new Response(JSON.stringify({
            success: false,
            error: 'IP地址不能为空'
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        }

        try {
          // 检查IP数量限制
          const maxIPs = env.MAX_WHITELIST_IPS;
          if (maxIPs) {
            const ipCount = await env.DB.prepare(`
              SELECT COUNT(*) as count FROM whitelist_ips WHERE proxy_id = ?
            `).bind(proxyId).first();

            const existingIP = await env.DB.prepare(`
              SELECT 1 FROM whitelist_ips WHERE proxy_id = ? AND ip_address = ?
            `).bind(proxyId, ip).first();

            if (!existingIP && ipCount.count >= parseInt(maxIPs)) {
              return new Response(JSON.stringify({
                success: false,
                error: `白名单IP数量已达到上限 ${maxIPs} 个`
              }), {
                headers: { 'Content-Type': 'application/json' }
              });
            }
          }

          // 添加IP到白名单（如果不存在）
          await env.DB.prepare(`
            INSERT OR IGNORE INTO whitelist_ips (proxy_id, ip_address)
            VALUES (?, ?)
          `).bind(proxyId, ip).run();

          // 获取当前IP总数
          const totalCount = await env.DB.prepare(`
            SELECT COUNT(*) as count FROM whitelist_ips WHERE proxy_id = ?
          `).bind(proxyId).first();

          return new Response(JSON.stringify({
            success: true,
            message: `IP ${ip} 已成功添加到代理 ${proxyId} 的白名单`,
            ip: ip,
            proxy_id: proxyId,
            total_ips: totalCount.count
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            error: `添加IP失败: ${error.message}`
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        }
      }

      if (subAction === 'remove_ip') {
        const ip = formData.get('ip_address');

        if (!ip) {
          return new Response(JSON.stringify({
            success: false,
            error: 'IP地址不能为空'
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        }

        try {
          // 删除指定IP
          await env.DB.prepare(`
            DELETE FROM whitelist_ips WHERE proxy_id = ? AND ip_address = ?
          `).bind(proxyId, ip).run();

          // 获取当前IP总数
          const totalCount = await env.DB.prepare(`
            SELECT COUNT(*) as count FROM whitelist_ips WHERE proxy_id = ?
          `).bind(proxyId).first();

          return new Response(JSON.stringify({
            success: true,
            message: `IP ${ip} 已从代理 ${proxyId} 的白名单中删除`,
            ip: ip,
            proxy_id: proxyId,
            total_ips: totalCount.count
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            error: `删除IP失败: ${error.message}`
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        }
      }

      if (subAction === 'get_info') {
        try {
          // 获取代理信息
          const proxy = await env.DB.prepare(`
            SELECT subscription_url FROM proxies WHERE id = ?
          `).bind(proxyId).first();

          // 获取白名单IP列表
          const ipResults = await env.DB.prepare(`
            SELECT ip_address FROM whitelist_ips WHERE proxy_id = ? ORDER BY created_at
          `).bind(proxyId).all();

          const ipList = ipResults.results.map(row => row.ip_address);

          const proxyUrl = `${new URL(request.url).origin}/proxy/${proxyId}`;
          const whitelistUrl = `${new URL(request.url).origin}/api/whitelist/${proxyId}?action=add`;
          const checkUrl = `${new URL(request.url).origin}/api/whitelist/${proxyId}`;
          const statusUrl = `${new URL(request.url).origin}/status/${proxyId}`;

          return new Response(JSON.stringify({
            success: true,
            proxy_id: proxyId,
            original_url: proxy.subscription_url,
            proxy_url: proxyUrl,
            whitelist_url: whitelistUrl,
            check_url: checkUrl,
            status_url: statusUrl,
            whitelist_ips: ipList,
            total_ips: ipList.length
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            error: `获取信息失败: ${error.message}`
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        }
      }

      if (subAction === 'delete_proxy') {
        try {
          // 删除白名单IP记录
          await env.DB.prepare(`
            DELETE FROM whitelist_ips WHERE proxy_id = ?
          `).bind(proxyId).run();

          // 删除代理记录
          await env.DB.prepare(`
            DELETE FROM proxies WHERE id = ?
          `).bind(proxyId).run();

          return new Response(JSON.stringify({
            success: true,
            message: `网关 ${proxyId} 已成功注销`,
            proxy_id: proxyId
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            error: `注销网关失败: ${error.message}`
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        }
      }
    }

    if (action === 'recover_password') {
      const subscriptionUrl = formData.get('subscription_url');

      if (!subscriptionUrl) {
        return new Response(JSON.stringify({
          success: false,
          error: '订阅地址不能为空'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 查找订阅地址对应的代理
      const proxy = await env.DB.prepare(`
        SELECT id, password FROM proxies WHERE subscription_url = ?
      `).bind(subscriptionUrl).first();

      if (!proxy) {
        return new Response(JSON.stringify({
          success: false,
          error: '未找到该订阅地址对应的代理'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 生成相关URL
      const proxyUrl = `${new URL(request.url).origin}/proxy/${proxy.id}`;
      const whitelistUrl = `${new URL(request.url).origin}/api/whitelist/${proxy.id}?action=add`;
      const statusUrl = `${new URL(request.url).origin}/status/${proxy.id}`;

      return new Response(JSON.stringify({
        success: true,
        proxy_id: proxy.id,
        proxy_password: proxy.password,
        subscription_url: subscriptionUrl,
        proxy_url: proxyUrl,
        whitelist_url: whitelistUrl,
        status_url: statusUrl
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }
  } catch (error) {
      console.error('处理POST请求时发生错误:', error);
      return new Response(JSON.stringify({
        success: false,
        error: `服务器内部错误: ${error.message}`
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  return new Response('不允许的请求方法', { status: 405 });
}

// 处理API请求
async function handleAPI(request, env, clientIP) {
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  
  if (pathParts[2] === 'whitelist' && pathParts[3]) {
    const proxyId = pathParts[3];
    const url = new URL(request.url);
    const action = url.searchParams.get('action');

    if (request.method === 'POST' || action === 'add') {
      // 添加当前IP到白名单
      try {
        // 检查IP数量限制
        const maxIPs = env.MAX_WHITELIST_IPS;
        if (maxIPs) {
          const ipCount = await env.DB.prepare(`
            SELECT COUNT(*) as count FROM whitelist_ips WHERE proxy_id = ?
          `).bind(proxyId).first();

          const existingIP = await env.DB.prepare(`
            SELECT 1 FROM whitelist_ips WHERE proxy_id = ? AND ip_address = ?
          `).bind(proxyId, clientIP).first();

          if (!existingIP && ipCount.count >= parseInt(maxIPs)) {
            const errorMessage = `白名单IP数量已达到上限 ${maxIPs} 个`;

            // 如果是GET请求且action=add，返回用户友好的错误页面
            if (request.method === 'GET' && action === 'add') {
              return new Response(getAddIPErrorHTML(clientIP, proxyId, errorMessage), {
                headers: { 'Content-Type': 'text/html' }
              });
            }

            return new Response(JSON.stringify({
              success: false,
              error: errorMessage,
              ip: clientIP,
              proxy_id: proxyId
            }), {
              headers: { 'Content-Type': 'application/json' }
            });
          }
        }

        // 添加IP到白名单（如果不存在）
        await env.DB.prepare(`
          INSERT OR IGNORE INTO whitelist_ips (proxy_id, ip_address)
          VALUES (?, ?)
        `).bind(proxyId, clientIP).run();

        // 获取当前IP总数
        const totalCount = await env.DB.prepare(`
          SELECT COUNT(*) as count FROM whitelist_ips WHERE proxy_id = ?
        `).bind(proxyId).first();

        // 如果是GET请求且action=add，返回用户友好的HTML页面
        if (request.method === 'GET' && action === 'add') {
          const baseUrl = new URL(request.url).origin;
          return new Response(getAddIPSuccessHTML(clientIP, proxyId, totalCount.count, baseUrl), {
            headers: { 'Content-Type': 'text/html' }
          });
        }

        // 否则返回JSON响应
        return new Response(JSON.stringify({
          success: true,
          message: `IP ${clientIP} 已成功添加到代理 ${proxyId} 的白名单`,
          ip: clientIP,
          proxy_id: proxyId,
          total_ips: totalCount.count
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (error) {
        // 如果是GET请求且action=add，返回用户友好的错误页面
        if (request.method === 'GET' && action === 'add') {
          return new Response(getAddIPErrorHTML(clientIP, proxyId, error.message), {
            headers: { 'Content-Type': 'text/html' }
          });
        }

        return new Response(JSON.stringify({
          success: false,
          error: `添加IP到白名单失败: ${error.message}`,
          ip: clientIP,
          proxy_id: proxyId
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    if (request.method === 'GET' && action !== 'add') {
      // 检查IP是否在白名单中
      try {
        // 检查当前IP是否在白名单中
        const isWhitelisted = await env.DB.prepare(`
          SELECT 1 FROM whitelist_ips WHERE proxy_id = ? AND ip_address = ?
        `).bind(proxyId, clientIP).first();

        // 获取所有白名单IP
        const ipResults = await env.DB.prepare(`
          SELECT ip_address FROM whitelist_ips WHERE proxy_id = ? ORDER BY created_at
        `).bind(proxyId).all();

        const ipList = ipResults.results.map(row => row.ip_address);

        return new Response(JSON.stringify({
          whitelisted: !!isWhitelisted,
          ip: clientIP,
          proxy_id: proxyId,
          total_ips: ipList.length,
          all_ips: ipList // 显示所有白名单IP用于调试
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (error) {
        return new Response(JSON.stringify({
          error: `检查白名单失败: ${error.message}`,
          ip: clientIP,
          proxy_id: proxyId
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }
  }
  
  return new Response('API接口未找到', { status: 404 });
}

// 处理代理请求
async function handleProxy(request, env, clientIP) {
  const url = new URL(request.url);
  const proxyId = url.pathname.split('/')[2];
  
  if (!proxyId) {
    return new Response('无效的网关ID', { status: 400 });
  }
  
  // 检查IP白名单
  const isWhitelisted = await env.DB.prepare(`
    SELECT 1 FROM whitelist_ips WHERE proxy_id = ? AND ip_address = ?
  `).bind(proxyId, clientIP).first();

  if (!isWhitelisted) {
    return new Response('访问被拒绝：IP不在白名单中', { status: 403 });
  }

  // 获取原始订阅地址
  const proxy = await env.DB.prepare(`
    SELECT subscription_url FROM proxies WHERE id = ?
  `).bind(proxyId).first();

  if (!proxy) {
    return new Response('订阅地址未找到', { status: 404 });
  }
  
  try {
    // 代理请求到原始地址
    const response = await fetch(proxy.subscription_url, {
      method: request.method,
      headers: request.headers,
      body: request.body
    });
    
    // 返回代理响应
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers
    });
  } catch (error) {
    return new Response('代理错误: ' + error.message, { status: 500 });
  }
}

// 处理状态检查页面
async function handleStatus(request, env, clientIP) {
  const url = new URL(request.url);
  const proxyId = url.pathname.split('/')[2];

  if (!proxyId) {
    return new Response('无效的网关ID', { status: 400 });
  }

  // 检查代理是否存在
  const proxy = await env.DB.prepare(`
    SELECT subscription_url FROM proxies WHERE id = ?
  `).bind(proxyId).first();

  if (!proxy) {
    return new Response(getStatusNotFoundHTML(proxyId), {
      headers: { 'Content-Type': 'text/html' }
    });
  }

  // 获取白名单状态
  try {
    // 检查当前IP是否在白名单中
    const isWhitelistedResult = await env.DB.prepare(`
      SELECT 1 FROM whitelist_ips WHERE proxy_id = ? AND ip_address = ?
    `).bind(proxyId, clientIP).first();

    // 获取白名单IP总数（不获取具体IP列表，保护隐私）
    const countResult = await env.DB.prepare(`
      SELECT COUNT(*) as total FROM whitelist_ips WHERE proxy_id = ?
    `).bind(proxyId).first();

    const totalIPs = countResult.total;
    const isWhitelisted = !!isWhitelistedResult;

    return new Response(getStatusHTML(proxyId, clientIP, isWhitelisted, totalIPs), {
      headers: { 'Content-Type': 'text/html' }
    });
  } catch (error) {
    return new Response(getStatusErrorHTML(proxyId, clientIP, error.message), {
      headers: { 'Content-Type': 'text/html' }
    });
  }
}

// 处理白名单助手页面
async function handleWhitelistHelper(request) {
  return new Response(getWhitelistHelperHTML(), {
    headers: { 'Content-Type': 'text/html' }
  });
}

// 生成随机ID
function generateId() {
  return Math.random().toString(36).substring(2, 15) +
         Math.random().toString(36).substring(2, 15);
}

// 生成随机密码
function generatePassword() {
  const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
  let password = '';
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}

// 获取首页HTML
function getHomeHTML() {
  return `<!DOCTYPE html>
<html>
<head>
    <title>白名单订阅网关</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.5em;
        }

        .card-icon.add { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-icon.ip { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .card-icon.view { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }

        .card h2 {
            color: #333;
            font-size: 1.5em;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
            font-size: 0.95em;
        }

        .input-wrapper {
            position: relative;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: #fafbfc;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .result {
            margin-top: 25px;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .result.success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-left-color: #28a745;
            color: #155724;
        }

        .result.error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border-left-color: #dc3545;
            color: #721c24;
        }

        .result h3 {
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .result p {
            margin-bottom: 10px;
            line-height: 1.6;
        }

        .result a {
            color: inherit;
            text-decoration: none;
            font-weight: 600;
            border-bottom: 1px dashed currentColor;
        }

        .result a:hover {
            border-bottom-style: solid;
        }

        .info-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }

        .info-box strong {
            color: #1976d2;
        }

        /* 模式切换样式 */
        .mode-switcher {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            background: white;
            border-radius: 15px;
            padding: 8px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .mode-btn {
            flex: 1;
            padding: 15px 20px;
            border: none;
            border-radius: 10px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: transparent;
            color: #666;
        }

        .mode-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .mode-btn:hover:not(.active) {
            background: #f5f5f5;
            color: #333;
        }

        /* 标签页样式 */
        .manage-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .tab-btn {
            padding: 12px 20px;
            border: none;
            background: transparent;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #666;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .tab-btn:hover:not(.active) {
            color: #333;
            background: #f5f5f5;
        }

        .tab-btn.danger {
            color: #dc3545;
        }

        .tab-btn.danger.active {
            color: #dc3545;
            border-bottom-color: #dc3545;
        }

        .tab-btn.danger:hover:not(.active) {
            color: #c82333;
            background: #f8d7da;
        }

        .tab-content {
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 危险区域样式 */
        .danger-zone {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: 2px solid #dc3545;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .danger-zone h3 {
            color: #721c24;
            margin-bottom: 15px;
        }

        .danger-zone ul {
            margin: 15px 0;
            padding-left: 20px;
        }

        .danger-zone li {
            margin: 8px 0;
            color: #721c24;
        }

        .confirm-section {
            margin-top: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
        }

        .confirm-section label {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            cursor: pointer;
            color: #721c24;
            font-weight: 500;
        }

        .confirm-section input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border: 2px solid #dc3545;
        }

        .btn-danger:hover:not(:disabled) {
            background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(220, 53, 69, 0.4);
        }

        .btn-danger:disabled {
            background: #6c757d;
            border-color: #6c757d;
            cursor: not-allowed;
            opacity: 0.6;
        }

        /* 小字体提示 */
        small {
            display: block;
            margin-top: 5px;
            color: #666;
            font-size: 0.85em;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .content {
                padding: 20px;
            }

            .card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 白名单订阅网关</h1>
            <p>创建和管理您的订阅网关，支持IP白名单保护</p>
        </div>

        <div class="content">
            <!-- 模式切换 -->
            <div class="mode-switcher">
                <button class="mode-btn active" onclick="switchMode('create')">🆕 创建新网关</button>
                <button class="mode-btn" onclick="switchMode('manage')">🛠️ 管理现有网关</button>
            </div>

            <!-- 创建网关模式 -->
            <div id="createMode" class="card">
                <div class="card-header">
                    <div class="card-icon add">➕</div>
                    <h2>创建新网关</h2>
                </div>
                <form id="createProxyForm">
                    <div class="form-group">
                        <label>🔗 原始订阅地址</label>
                        <input type="url" id="originalUrl" placeholder="https://example.com/subscribe/your-subscription" required>
                        <small>输入您要网关的订阅地址</small>
                    </div>
                    <button type="submit" class="btn">创建网关</button>
                </form>
                <div id="createResult"></div>
            </div>

            <!-- 管理网关模式 -->
            <div id="manageMode" class="card" style="display: none;">
                <div class="card-header">
                    <div class="card-icon ip">🛠️</div>
                    <h2>管理现有网关</h2>
                </div>

                <!-- 登录表单 -->
                <div id="loginForm">
                    <form id="proxyLoginForm">
                        <div class="form-group">
                            <label>🆔 网关ID 或 📡 订阅地址</label>
                            <input type="text" id="manageProxyId" placeholder="请输入网关ID或完整的订阅地址" required>
                            <small>支持使用网关ID或原始订阅地址进行查询</small>
                        </div>
                        <div class="form-group">
                            <label>🔐 网关密码</label>
                            <input type="password" id="manageProxyPassword" placeholder="请输入网关密码" required>
                            <small>创建网关时生成的管理密码</small>
                        </div>
                        <button type="submit" class="btn">🔑 登录管理</button>
                    </form>

                    <!-- 忘记密码链接 -->
                    <div style="text-align: center; margin-top: 15px;">
                        <a href="#" id="forgotPasswordLink" style="color: #007cba; text-decoration: none; font-size: 14px;">
                            🔍 忘记密码？通过订阅地址找回
                        </a>
                    </div>
                </div>

                <!-- 密码找回表单 -->
                <div id="recoverForm" style="display: none;">
                    <form id="passwordRecoverForm">
                        <div class="form-group">
                            <label>📡 原始订阅地址</label>
                            <input type="text" id="recoverSubscriptionUrl" placeholder="请输入完整的原始订阅地址" required>
                            <small>输入创建网关时使用的原始订阅地址</small>
                        </div>
                        <button type="submit" class="btn">🔍 查找密码</button>
                    </form>

                    <!-- 返回登录链接 -->
                    <div style="text-align: center; margin-top: 15px;">
                        <a href="#" id="backToLoginLink" style="color: #007cba; text-decoration: none; font-size: 14px;">
                            ← 返回登录
                        </a>
                    </div>

                    <div id="recoverResult"></div>
                </div>

                <!-- 管理界面 -->
                <div id="manageInterface" style="display: none;">
                    <div class="manage-tabs">
                        <button class="tab-btn active" onclick="switchTab('info')">📊 网关信息</button>
                        <button class="tab-btn" onclick="switchTab('addip')">➕ 添加IP</button>
                        <button class="tab-btn danger" onclick="switchTab('delete')">🗑️ 注销网关</button>
                    </div>

                    <!-- 返回创建按钮 -->
                    <div style="text-align: center; margin-bottom: 15px;">
                        <button onclick="returnToCreate()" class="btn" style="background: #6c757d; font-size: 14px; padding: 8px 16px;">
                            ← 返回创建新网关
                        </button>
                    </div>

                    <!-- 代理信息标签 -->
                    <div id="infoTab" class="tab-content">
                        <div id="proxyInfo"></div>
                    </div>

                    <!-- 添加IP标签 -->
                    <div id="addipTab" class="tab-content" style="display: none;">
                        <form id="addIPForm">
                            <div class="form-group">
                                <label>📍 IP地址</label>
                                <input type="text" id="ipAddress" placeholder="例如: *********** 或批量: ***********,*******,2001:db8::1" required>
                                <small style="color: #6c757d; font-size: 0.9em; margin-top: 5px; display: block;">
                                    💡 支持单个IP或多个IP（用逗号分隔），支持IPv4和IPv6格式
                                </small>
                            </div>
                            <button type="submit" class="btn">添加IP到白名单</button>
                        </form>
                        <div id="addIPResult"></div>
                    </div>

                    <!-- 注销网关标签 -->
                    <div id="deleteTab" class="tab-content" style="display: none;">
                        <div class="danger-zone">
                            <h3>⚠️ 危险操作</h3>
                            <p>注销网关将会：</p>
                            <ul>
                                <li>🗑️ 永久删除此网关的所有配置</li>
                                <li>🚫 删除所有白名单IP记录</li>
                                <li>❌ 使网关地址立即失效</li>
                                <li>⚠️ 此操作不可撤销！</li>
                            </ul>
                            <div class="confirm-section">
                                <label>
                                    <input type="checkbox" id="confirmDelete">
                                    我确认要注销此网关，并了解此操作不可撤销
                                </label>
                                <button id="deleteProxyBtn" class="btn btn-danger" disabled onclick="deleteProxy()">
                                    🗑️ 确认注销网关
                                </button>
                            </div>
                        </div>
                        <div id="deleteResult"></div>
                    </div>
                </div>

                <div id="manageResult"></div>
            </div>


        </div>
    </div>

    <script>
        // 添加加载动画
        function showLoading(buttonElement) {
            const originalText = buttonElement.textContent;
            buttonElement.textContent = '处理中...';
            buttonElement.disabled = true;
            return originalText;
        }

        function hideLoading(buttonElement, originalText) {
            buttonElement.textContent = originalText;
            buttonElement.disabled = false;
        }

        // 模式切换功能
        function switchMode(mode) {
            const createBtn = document.querySelector('.mode-btn:first-child');
            const manageBtn = document.querySelector('.mode-btn:last-child');
            const createMode = document.getElementById('createMode');
            const manageMode = document.getElementById('manageMode');

            if (mode === 'create') {
                createBtn.classList.add('active');
                manageBtn.classList.remove('active');
                createMode.style.display = 'block';
                manageMode.style.display = 'none';
            } else {
                createBtn.classList.remove('active');
                manageBtn.classList.add('active');
                createMode.style.display = 'none';
                manageMode.style.display = 'block';
            }
        }

        // 标签页切换功能
        function switchTab(tab) {
            const tabBtns = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');

            tabBtns.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.style.display = 'none');

            document.querySelector(\`[onclick="switchTab('\${tab}')"]\`).classList.add('active');
            document.getElementById(\`\${tab}Tab\`).style.display = 'block';
        }

        // 复制到剪贴板功能
        function copyToClipboard(text, buttonElement) {
            navigator.clipboard.writeText(text).then(function() {
                const originalText = buttonElement.textContent;
                buttonElement.textContent = '✅ 已复制';
                buttonElement.style.background = '#28a745';
                setTimeout(() => {
                    buttonElement.textContent = originalText;
                    buttonElement.style.background = '';
                }, 2000);
            }, function(err) {
                console.error('无法复制文本: ', err);
                alert('复制失败，请手动复制');
            });
        }

        // 自动登录到管理界面
        window.autoLoginToManage = function(proxyId, proxyPassword) {
            // 安全检查：确保参数不为空且格式正确
            if (!proxyId || !proxyPassword || typeof proxyId !== 'string' || typeof proxyPassword !== 'string') {
                alert('登录信息无效，请手动登录管理界面。');
                return;
            }

            // 切换到管理模式
            switchMode('manage');

            // 填入登录信息（注意：这里只是临时填入，不会持久化存储）
            document.getElementById('manageProxyId').value = proxyId;
            document.getElementById('manageProxyPassword').value = proxyPassword;

            // 清空创建结果显示
            document.getElementById('createResult').innerHTML = '';

            // 显示自动登录提示
            document.getElementById('manageResult').innerHTML = \`
                <div class="result info" style="background: #e3f2fd; border: 1px solid #2196f3; color: #1565c0;">
                    <h3>🔄 正在自动登录...</h3>
                    <p>正在使用刚创建的网关信息自动登录管理界面...</p>
                </div>
            \`;

            // 自动提交登录表单
            setTimeout(() => {
                console.log('开始自动登录，网关ID:', proxyId, '密码长度:', proxyPassword.length);
                try {
                    const form = document.getElementById('proxyLoginForm');
                    if (form) {
                        form.dispatchEvent(new Event('submit'));
                        console.log('自动登录表单已提交');
                    } else {
                        console.error('找不到登录表单');
                        document.getElementById('manageResult').innerHTML = \`
                            <div class="result error">
                                <h3>❌ 自动登录失败</h3>
                                <p>找不到登录表单，请手动登录。</p>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('自动登录出错:', error);
                    document.getElementById('manageResult').innerHTML = \`
                        <div class="result error">
                            <h3>❌ 自动登录失败</h3>
                            <p>错误详情：\${error.message}</p>
                            <p>请手动登录管理界面。</p>
                        </div>
                    \`;
                }
            }, 500);
        };

        // 返回创建新网关
        window.returnToCreate = function() {
            // 清除所有表单数据和保存的密码
            document.getElementById('manageProxyId').value = '';
            document.getElementById('manageProxyPassword').value = '';
            currentProxyPassword = '';
            currentProxyId = '';

            // 清除会话状态
            clearSessionState();

            // 重置管理界面状态
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('manageInterface').style.display = 'none';
            document.getElementById('recoverForm').style.display = 'none';
            document.getElementById('manageResult').innerHTML = '';

            // 切换到创建模式
            switchMode('create');
        };

        // 创建网关表单处理
        document.getElementById('createProxyForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = showLoading(submitBtn);

            const formData = new FormData();
            formData.append('action', 'create_proxy');
            formData.append('original_url', document.getElementById('originalUrl').value);

            try {
                const response = await fetch('/', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('createResult').innerHTML = \`
                        <div class="result success">
                            <h3>🎉 网关创建成功！</h3>
                            <p><strong>🆔 网关ID：</strong> <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 4px; font-family: monospace;">\${result.proxy_id}</code>
                               <button onclick="copyToClipboard('\${result.proxy_id}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>🔐 管理密码：</strong> <code style="background: #fff3cd; padding: 2px 6px; border-radius: 4px; font-family: monospace; color: #856404;">\${result.proxy_password}</code>
                               <button onclick="copyToClipboard('\${result.proxy_password}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>🔗 网关地址：</strong> <br><a href="\${result.proxy_url}" target="_blank" style="word-break: break-all; display: inline-block; max-width: 100%; background: #f8f9fa; padding: 8px; border-radius: 4px; margin-top: 5px; text-decoration: none; color: #007cba; border: 1px solid #e9ecef;">\${result.proxy_url}</a>
                               <button onclick="copyToClipboard('\${result.proxy_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 5px;">复制</button></p>
                            <p><strong>✨ 白名单地址（用户使用）：</strong> <br><a href="\${result.whitelist_url}" target="_blank" style="word-break: break-all; display: inline-block; max-width: 100%; background: #f8f9fa; padding: 8px; border-radius: 4px; margin-top: 5px; text-decoration: none; color: #007cba; border: 1px solid #e9ecef;">\${result.whitelist_url}</a>
                               <button onclick="copyToClipboard('\${result.whitelist_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 5px;">复制</button></p>
                            <p><strong>🎨 状态检查页面（UI）：</strong> <br><a href="\${result.status_url}" target="_blank" style="word-break: break-all; display: inline-block; max-width: 100%; background: #f8f9fa; padding: 8px; border-radius: 4px; margin-top: 5px; text-decoration: none; color: #007cba; border: 1px solid #e9ecef;">\${result.status_url}</a>
                               <button onclick="copyToClipboard('\${result.status_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 5px;">复制</button></p>
                            <div class="info-box">
                                <strong>⚠️ 重要提醒：</strong><br>
                                • <strong>请保存管理密码：</strong> 用于管理此网关的白名单<br>
                                • <strong>分享白名单地址：</strong> 用户点击即可自动添加IP到白名单<br>
                                • <strong>状态检查页面：</strong> 提供美观的白名单状态查看界面
                            </div>
                            <div style="text-align: center; margin-top: 20px;">
                                <button onclick="autoLoginToManage('\${result.proxy_id}', '\${result.proxy_password}')" class="btn" style="background: #28a745;">
                                    🛠️ 立即进入管理界面
                                </button>
                            </div>
                        </div>
                    \`;

                    // 清空输入框
                    document.getElementById('originalUrl').value = '';
                } else {
                    document.getElementById('createResult').innerHTML = \`
                        <div class="result error">
                            <h3>❌ 创建网关失败</h3>
                            <p>错误详情：\${result.error || '未知错误'}</p>
                            <p>请检查订阅地址是否正确，或稍后重试。</p>
                        </div>
                    \`;
                }
            } catch (error) {
                document.getElementById('createResult').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>错误详情：\${error.message}</p>
                    </div>
                \`;
            } finally {
                hideLoading(submitBtn, originalText);
            }
        });

        // 全局变量保存当前会话的密码
        let currentProxyPassword = '';
        let currentProxyId = '';

        // 刷新网关信息函数
        async function refreshProxyInfo() {
            if (!currentProxyId || !currentProxyPassword) {
                console.error('缺少网关ID或密码，无法刷新信息');
                return;
            }

            try {
                const formData = new FormData();
                formData.append('action', 'manage_proxy');
                formData.append('sub_action', 'get_info');
                formData.append('proxy_id', currentProxyId);
                formData.append('proxy_password', currentProxyPassword);

                const response = await fetch('/', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // 更新网关信息显示
                    document.getElementById('proxyInfo').innerHTML = \`
                        <div class="result success">
                            <h3>📊 网关信息</h3>
                            <p><strong>🆔 网关ID：</strong> <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 4px; font-family: monospace;">\${result.proxy_id}</code>
                               <button onclick="copyToClipboard('\${result.proxy_id}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>🔐 管理密码：</strong> <code style="background: #fff3cd; padding: 2px 6px; border-radius: 4px; font-family: monospace; color: #856404;">\${currentProxyPassword}</code>
                               <button onclick="copyToClipboard('\${currentProxyPassword}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>🔗 原始订阅地址：</strong> <br><a href="\${result.original_url}" target="_blank" style="word-break: break-all; display: inline-block; max-width: 100%; background: #f8f9fa; padding: 8px; border-radius: 4px; margin-top: 5px; text-decoration: none; color: #007cba; border: 1px solid #e9ecef;">\${result.original_url}</a>
                               <button onclick="copyToClipboard('\${result.original_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 5px;">复制</button></p>
                            <p><strong>🚀 网关地址：</strong> <br><a href="\${result.proxy_url}" target="_blank" style="word-break: break-all; display: inline-block; max-width: 100%; background: #f8f9fa; padding: 8px; border-radius: 4px; margin-top: 5px; text-decoration: none; color: #007cba; border: 1px solid #e9ecef;">\${result.proxy_url}</a>
                               <button onclick="copyToClipboard('\${result.proxy_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 5px;">复制</button></p>
                            <p><strong>✨ 白名单地址：</strong> <br><a href="\${result.whitelist_url}" target="_blank" style="word-break: break-all; display: inline-block; max-width: 100%; background: #f8f9fa; padding: 8px; border-radius: 4px; margin-top: 5px; text-decoration: none; color: #007cba; border: 1px solid #e9ecef;">\${result.whitelist_url}</a>
                               <button onclick="copyToClipboard('\${result.whitelist_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 5px;">复制</button></p>
                            <p><strong>🎨 状态检查页面：</strong> <br><a href="\${result.status_url}" target="_blank" style="word-break: break-all; display: inline-block; max-width: 100%; background: #f8f9fa; padding: 8px; border-radius: 4px; margin-top: 5px; text-decoration: none; color: #007cba; border: 1px solid #e9ecef;">\${result.status_url}</a>
                               <button onclick="copyToClipboard('\${result.status_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 5px;">复制</button></p>
                            <p><strong>📊 白名单IP数量：</strong> \${result.total_ips} 个</p>
                            \${result.whitelist_ips.length > 0 ? \`
                                <div style="margin-top: 15px;">
                                    <strong>📝 白名单IP列表：</strong>
                                    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-top: 5px; max-height: 200px; overflow-y: auto;">
                                        \${result.whitelist_ips.map((ip, index) => \`
                                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #e9ecef;">
                                                <span style="display: flex; align-items: center;">
                                                    <span style="display: inline-block; width: 8px; height: 8px; background: #007cba; border-radius: 50%; margin-right: 10px;"></span>
                                                    <code style="background: rgba(0,124,186,0.1); padding: 2px 6px; border-radius: 4px; font-family: 'Courier New', monospace;">\${ip}</code>
                                                </span>
                                                <button onclick="removeIP('\${ip}')" style="background: #dc3545; color: white; border: none; border-radius: 4px; padding: 2px 8px; font-size: 12px; cursor: pointer;" title="删除此IP">🗑️</button>
                                            </div>
                                        \`).join('')}
                                    </div>
                                </div>
                            \` : '<p style="color: #666; margin-top: 10px;">暂无白名单IP</p>'}
                        </div>
                    \`;
                } else {
                    console.error('刷新网关信息失败:', result.error);
                }
            } catch (error) {
                console.error('刷新网关信息时发生错误:', error);
            }
        }

        // 页面加载时检查会话状态
        window.addEventListener('DOMContentLoaded', function() {
            checkSessionState();
        });

        // 检查会话状态
        function checkSessionState() {
            const sessionData = sessionStorage.getItem('proxySession');
            if (sessionData) {
                try {
                    const session = JSON.parse(sessionData);
                    const now = Date.now();

                    // 检查会话是否过期（30分钟）
                    if (session.timestamp && (now - session.timestamp) < 30 * 60 * 1000) {
                        // 会话有效，显示重新登录提示
                        if (session.proxyId) {
                            showSessionRecoveryPrompt(session.proxyId);
                        }
                    } else {
                        // 会话过期，清除
                        sessionStorage.removeItem('proxySession');
                    }
                } catch (error) {
                    console.error('解析会话数据失败:', error);
                    sessionStorage.removeItem('proxySession');
                }
            }
        }

        // 显示会话恢复提示
        function showSessionRecoveryPrompt(proxyId) {
            // 切换到管理模式
            switchMode('manage');

            // 填入网关ID
            document.getElementById('manageProxyId').value = proxyId;

            // 显示友好的重新登录提示
            document.getElementById('manageResult').innerHTML = \`
                <div class="result info" style="background: #fff3cd; border: 1px solid #ffc107; color: #856404;">
                    <h3>🔄 检测到之前的会话</h3>
                    <p><strong>网关ID：</strong> <code>\${proxyId}</code></p>
                    <p>为了您的安全，请重新输入管理密码以继续管理此网关。</p>
                    <p style="font-size: 0.9em; color: #6c757d; margin-top: 10px;">
                        💡 提示：如果您忘记了密码，可以点击上方的"忘记密码？"链接通过订阅地址找回。
                    </p>
                </div>
            \`;
        }

        // 保存会话状态
        function saveSessionState(proxyId) {
            const sessionData = {
                proxyId: proxyId,
                timestamp: Date.now()
            };
            sessionStorage.setItem('proxySession', JSON.stringify(sessionData));
        }

        // 清除会话状态
        function clearSessionState() {
            sessionStorage.removeItem('proxySession');
        }

        // IP地址格式验证函数
        function validateIPAddress(ip) {
            // 去除首尾空格
            ip = ip.trim();

            // IPv4 正则表达式
            const ipv4Regex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

            // IPv6 正则表达式（简化版，支持常见格式）
            const ipv6Regex = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/;

            if (ipv4Regex.test(ip)) {
                return { valid: true, type: 'IPv4', ip: ip };
            } else if (ipv6Regex.test(ip)) {
                return { valid: true, type: 'IPv6', ip: ip };
            } else {
                return { valid: false, type: null, ip: ip };
            }
        }

        // 批量IP地址验证函数
        function validateBatchIPAddresses(input) {
            // 去除首尾空格并按逗号分割
            const ips = input.trim().split(',').map(ip => ip.trim()).filter(ip => ip.length > 0);

            if (ips.length === 0) {
                return { valid: false, error: '请输入至少一个IP地址' };
            }

            const validIPs = [];
            const invalidIPs = [];

            for (const ip of ips) {
                const validation = validateIPAddress(ip);
                if (validation.valid) {
                    validIPs.push(validation);
                } else {
                    invalidIPs.push(ip);
                }
            }

            if (invalidIPs.length > 0) {
                return {
                    valid: false,
                    error: '以下IP地址格式无效：' + invalidIPs.join(', '),
                    validIPs: validIPs,
                    invalidIPs: invalidIPs
                };
            }

            return { valid: true, ips: validIPs };
        }

        // 代理登录表单处理
        document.getElementById('proxyLoginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            console.log('登录表单提交事件触发');

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = showLoading(submitBtn);

            const proxyId = document.getElementById('manageProxyId').value;
            const proxyPassword = document.getElementById('manageProxyPassword').value;

            console.log('登录信息 - 网关ID:', proxyId, '密码长度:', proxyPassword.length);

            const formData = new FormData();
            formData.append('action', 'manage_proxy');
            formData.append('sub_action', 'get_info');
            formData.append('proxy_id', proxyId);
            formData.append('proxy_password', proxyPassword);

            try {
                const response = await fetch('/', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // 保存密码到全局变量（用于后续操作）
                    currentProxyPassword = proxyPassword;
                    currentProxyId = proxyId;

                    // 保存会话状态
                    saveSessionState(proxyId);

                    // 清除密码字段（安全措施）
                    document.getElementById('manageProxyPassword').value = '';

                    // 隐藏登录表单，显示管理界面
                    document.getElementById('loginForm').style.display = 'none';
                    document.getElementById('manageInterface').style.display = 'block';

                    // 清除自动登录提示信息
                    document.getElementById('manageResult').innerHTML = '';

                    // 显示代理信息
                    document.getElementById('proxyInfo').innerHTML = \`
                        <div class="result success">
                            <h3>📊 网关信息</h3>
                            <p><strong>🆔 网关ID：</strong> <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 4px; font-family: monospace;">\${result.proxy_id}</code>
                               <button onclick="copyToClipboard('\${result.proxy_id}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>🔐 管理密码：</strong> <code style="background: #fff3cd; padding: 2px 6px; border-radius: 4px; font-family: monospace; color: #856404;">\${currentProxyPassword}</code>
                               <button onclick="copyToClipboard('\${currentProxyPassword}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>🔗 原始订阅地址：</strong> <br><a href="\${result.original_url}" target="_blank" style="word-break: break-all; display: inline-block; max-width: 100%; background: #f8f9fa; padding: 8px; border-radius: 4px; margin-top: 5px; text-decoration: none; color: #007cba; border: 1px solid #e9ecef;">\${result.original_url}</a>
                               <button onclick="copyToClipboard('\${result.original_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 5px;">复制</button></p>
                            <p><strong>🚀 网关地址：</strong> <br><a href="\${result.proxy_url}" target="_blank" style="word-break: break-all; display: inline-block; max-width: 100%; background: #f8f9fa; padding: 8px; border-radius: 4px; margin-top: 5px; text-decoration: none; color: #007cba; border: 1px solid #e9ecef;">\${result.proxy_url}</a>
                               <button onclick="copyToClipboard('\${result.proxy_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 5px;">复制</button></p>
                            <p><strong>✨ 白名单地址：</strong> <br><a href="\${result.whitelist_url}" target="_blank" style="word-break: break-all; display: inline-block; max-width: 100%; background: #f8f9fa; padding: 8px; border-radius: 4px; margin-top: 5px; text-decoration: none; color: #007cba; border: 1px solid #e9ecef;">\${result.whitelist_url}</a>
                               <button onclick="copyToClipboard('\${result.whitelist_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 5px;">复制</button></p>
                            <p><strong>🎨 状态检查页面：</strong> <br><a href="\${result.status_url}" target="_blank" style="word-break: break-all; display: inline-block; max-width: 100%; background: #f8f9fa; padding: 8px; border-radius: 4px; margin-top: 5px; text-decoration: none; color: #007cba; border: 1px solid #e9ecef;">\${result.status_url}</a>
                               <button onclick="copyToClipboard('\${result.status_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 5px;">复制</button></p>
                            <p><strong>📊 白名单IP数量：</strong> \${result.total_ips} 个</p>
                            \${result.whitelist_ips.length > 0 ? \`
                                <div style="margin-top: 15px;">
                                    <strong>📝 白名单IP列表：</strong>
                                    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-top: 5px; max-height: 200px; overflow-y: auto;">
                                        \${result.whitelist_ips.map((ip, index) => \`
                                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #e9ecef;">
                                                <span style="display: flex; align-items: center;">
                                                    <span style="display: inline-block; width: 8px; height: 8px; background: #007cba; border-radius: 50%; margin-right: 10px;"></span>
                                                    <code style="background: rgba(0,124,186,0.1); padding: 2px 6px; border-radius: 4px; font-family: 'Courier New', monospace;">\${ip}</code>
                                                </span>
                                                <button onclick="removeIP('\${ip}')" style="background: #dc3545; color: white; border: none; border-radius: 4px; padding: 2px 8px; font-size: 12px; cursor: pointer;" title="删除此IP">🗑️</button>
                                            </div>
                                        \`).join('')}
                                    </div>
                                </div>
                            \` : '<p style="color: #666; margin-top: 10px;">暂无白名单IP</p>'}
                        </div>
                    \`;
                } else {
                    document.getElementById('manageResult').innerHTML = \`
                        <div class="result error">
                            <h3>❌ 登录失败</h3>
                            <p>错误详情：\${result.error || '未知错误'}</p>
                            <p>请检查网关ID和密码是否正确。</p>
                        </div>
                    \`;
                }
            } catch (error) {
                document.getElementById('manageResult').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>错误详情：\${error.message}</p>
                    </div>
                \`;
            } finally {
                hideLoading(submitBtn, originalText);
            }
        });

        // 添加IP表单处理
        document.getElementById('addIPForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = showLoading(submitBtn);

            // 获取并验证IP地址（支持批量）
            const ipInput = document.getElementById('ipAddress').value;
            const batchValidation = validateBatchIPAddresses(ipInput);

            // 如果IP格式无效，显示错误并返回
            if (!batchValidation.valid) {
                document.getElementById('addIPResult').innerHTML = \`
                    <div class="result error">
                        <h3>❌ IP地址格式错误</h3>
                        <p><strong>错误详情：</strong> \${batchValidation.error}</p>
                        <p>请输入有效的IPv4或IPv6地址格式：</p>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>单个IP：</strong> ***********</li>
                            <li><strong>批量IP：</strong> ***********,*******,2001:db8::1</li>
                            <li><strong>支持格式：</strong> IPv4和IPv6，用逗号分隔</li>
                        </ul>
                    </div>
                \`;
                hideLoading(submitBtn, originalText);
                return;
            }

            // 批量添加IP
            const results = [];
            let successCount = 0;
            let failCount = 0;

            for (const ipInfo of batchValidation.ips) {
                const formData = new FormData();
                formData.append('action', 'manage_proxy');
                formData.append('sub_action', 'add_ip');
                formData.append('proxy_id', document.getElementById('manageProxyId').value);
                formData.append('proxy_password', currentProxyPassword);
                formData.append('ip_address', ipInfo.ip);

                try {
                    const response = await fetch('/', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success) {
                        results.push({
                            ip: ipInfo.ip,
                            type: ipInfo.type,
                            success: true,
                            total_ips: result.total_ips
                        });
                        successCount++;
                    } else {
                        results.push({
                            ip: ipInfo.ip,
                            type: ipInfo.type,
                            success: false,
                            error: result.error || '未知错误'
                        });
                        failCount++;
                    }
                } catch (error) {
                    results.push({
                        ip: ipInfo.ip,
                        type: ipInfo.type,
                        success: false,
                        error: error.message
                    });
                    failCount++;
                }
            }

            // 显示批量添加结果
            const totalCount = batchValidation.ips.length;
            const lastSuccessResult = results.find(r => r.success);

            let resultHTML = '';
            if (successCount > 0 && failCount === 0) {
                // 全部成功
                resultHTML = \`
                    <div class="result success">
                        <h3>🎉 批量添加完成！</h3>
                        <p><strong>📊 成功添加：</strong> \${successCount} 个IP</p>
                        \${lastSuccessResult ? \`<p><strong>📈 白名单总数：</strong> \${lastSuccessResult.total_ips} 个IP</p>\` : ''}
                        <div style="margin-top: 15px;">
                            <strong>✅ 成功添加的IP：</strong>
                            <ul style="margin: 10px 0; padding-left: 20px;">
                                \${results.filter(r => r.success).map(r => \`<li><code>\${r.ip}</code> (\${r.type})</li>\`).join('')}
                            </ul>
                        </div>
                    </div>
                \`;
            } else if (successCount > 0 && failCount > 0) {
                // 部分成功
                resultHTML = \`
                    <div class="result warning" style="background: #fff3cd; border: 1px solid #ffc107; color: #856404;">
                        <h3>⚠️ 批量添加部分完成</h3>
                        <p><strong>📊 成功：</strong> \${successCount} 个IP，<strong>失败：</strong> \${failCount} 个IP</p>
                        \${lastSuccessResult ? \`<p><strong>📈 白名单总数：</strong> \${lastSuccessResult.total_ips} 个IP</p>\` : ''}

                        \${successCount > 0 ? \`
                        <div style="margin-top: 15px;">
                            <strong>✅ 成功添加的IP：</strong>
                            <ul style="margin: 10px 0; padding-left: 20px;">
                                \${results.filter(r => r.success).map(r => \`<li><code>\${r.ip}</code> (\${r.type})</li>\`).join('')}
                            </ul>
                        </div>
                        \` : ''}

                        <div style="margin-top: 15px;">
                            <strong>❌ 添加失败的IP：</strong>
                            <ul style="margin: 10px 0; padding-left: 20px;">
                                \${results.filter(r => !r.success).map(r => \`<li><code>\${r.ip}</code> (\${r.type}) - \${r.error}</li>\`).join('')}
                            </ul>
                        </div>
                    </div>
                \`;
            } else {
                // 全部失败
                resultHTML = \`
                    <div class="result error">
                        <h3>❌ 批量添加失败</h3>
                        <p><strong>📊 失败：</strong> \${failCount} 个IP</p>
                        <div style="margin-top: 15px;">
                            <strong>❌ 添加失败的IP：</strong>
                            <ul style="margin: 10px 0; padding-left: 20px;">
                                \${results.filter(r => !r.success).map(r => \`<li><code>\${r.ip}</code> (\${r.type}) - \${r.error}</li>\`).join('')}
                            </ul>
                        </div>
                    </div>
                \`;
            }

            document.getElementById('addIPResult').innerHTML = resultHTML;

            // 如果有成功添加的IP，清空输入框并刷新列表
            if (successCount > 0) {
                document.getElementById('ipAddress').value = '';

                // 刷新白名单列表
                setTimeout(() => {
                    refreshProxyInfo();
                }, 1000);
            }

            hideLoading(submitBtn, originalText);
        });

        // 删除IP函数
        window.removeIP = async function(ip) {
            if (!confirm(\`确定要删除IP \${ip} 吗？\`)) {
                return;
            }

            const formData = new FormData();
            formData.append('action', 'manage_proxy');
            formData.append('sub_action', 'remove_ip');
            formData.append('proxy_id', document.getElementById('manageProxyId').value);
            formData.append('proxy_password', currentProxyPassword);
            formData.append('ip_address', ip);

            try {
                const response = await fetch('/', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // 重新获取代理信息以刷新列表
                    refreshProxyInfo();
                    alert(\`IP \${ip} 已成功删除\`);
                } else {
                    alert(\`删除失败：\${result.error || '未知错误'}\`);
                }
            } catch (error) {
                alert(\`删除失败：\${error.message}\`);
            }
        };

        // 忘记密码链接处理
        document.getElementById('forgotPasswordLink').addEventListener('click', (e) => {
            e.preventDefault();
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('recoverForm').style.display = 'block';
            document.getElementById('manageResult').innerHTML = '';
        });

        // 返回登录链接处理
        document.getElementById('backToLoginLink').addEventListener('click', (e) => {
            e.preventDefault();
            document.getElementById('recoverForm').style.display = 'none';
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('recoverResult').innerHTML = '';
        });

        // 密码找回表单处理
        document.getElementById('passwordRecoverForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = showLoading(submitBtn);

            const formData = new FormData();
            formData.append('action', 'recover_password');
            formData.append('subscription_url', document.getElementById('recoverSubscriptionUrl').value);

            try {
                const response = await fetch('/', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('recoverResult').innerHTML = \`
                        <div class="result success">
                            <h3>🎉 密码找回成功！</h3>
                            <p><strong>🆔 网关ID：</strong> <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 4px; font-family: monospace;">\${result.proxy_id}</code>
                               <button onclick="copyToClipboard('\${result.proxy_id}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>🔐 管理密码：</strong> <code style="background: #fff3cd; padding: 2px 6px; border-radius: 4px; font-family: monospace; color: #856404;">\${result.proxy_password}</code>
                               <button onclick="copyToClipboard('\${result.proxy_password}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>📡 订阅地址：</strong> <br><a href="\${result.subscription_url}" target="_blank" style="word-break: break-all; display: inline-block; max-width: 100%; background: #f8f9fa; padding: 8px; border-radius: 4px; margin-top: 5px; text-decoration: none; color: #007cba; border: 1px solid #e9ecef;">\${result.subscription_url}</a></p>
                            <p><strong>🔗 网关地址：</strong> <br><a href="\${result.proxy_url}" target="_blank" style="word-break: break-all; display: inline-block; max-width: 100%; background: #f8f9fa; padding: 8px; border-radius: 4px; margin-top: 5px; text-decoration: none; color: #007cba; border: 1px solid #e9ecef;">\${result.proxy_url}</a>
                               <button onclick="copyToClipboard('\${result.proxy_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 5px;">复制</button></p>
                            <p><strong>✨ 白名单地址：</strong> <br><a href="\${result.whitelist_url}" target="_blank" style="word-break: break-all; display: inline-block; max-width: 100%; background: #f8f9fa; padding: 8px; border-radius: 4px; margin-top: 5px; text-decoration: none; color: #007cba; border: 1px solid #e9ecef;">\${result.whitelist_url}</a>
                               <button onclick="copyToClipboard('\${result.whitelist_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 5px;">复制</button></p>
                            <p><strong>🎨 状态检查页面：</strong> <br><a href="\${result.status_url}" target="_blank" style="word-break: break-all; display: inline-block; max-width: 100%; background: #f8f9fa; padding: 8px; border-radius: 4px; margin-top: 5px; text-decoration: none; color: #007cba; border: 1px solid #e9ecef;">\${result.status_url}</a>
                               <button onclick="copyToClipboard('\${result.status_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 5px;">复制</button></p>
                            <div class="info-box">
                                <strong>💡 提示：</strong><br>
                                • 现在您可以使用找回的网关ID和密码登录管理界面<br>
                                • 请妥善保存这些信息，避免再次丢失<br>
                                • 您可以点击上方的"返回登录"链接进行登录
                            </div>
                        </div>
                    \`;
                    // 清空输入框
                    document.getElementById('recoverSubscriptionUrl').value = '';
                } else {
                    document.getElementById('recoverResult').innerHTML = \`
                        <div class="result error">
                            <h3>❌ 密码找回失败</h3>
                            <p>错误详情：\${result.error || '未知错误'}</p>
                            <p>请检查订阅地址是否正确，或确认该订阅地址是否已创建过网关。</p>
                        </div>
                    \`;
                }
            } catch (error) {
                document.getElementById('recoverResult').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>错误详情：\${error.message}</p>
                    </div>
                \`;
            } finally {
                hideLoading(submitBtn, originalText);
            }
        });

        // 确认复选框处理
        document.addEventListener('change', function(e) {
            if (e.target.id === 'confirmDelete') {
                const deleteBtn = document.getElementById('deleteProxyBtn');
                deleteBtn.disabled = !e.target.checked;
            }
        });

        // 删除网关函数
        window.deleteProxy = async function() {
            const proxyId = document.getElementById('manageProxyId').value;

            if (!confirm('⚠️ 最终确认：您确定要永久删除网关 ' + proxyId + ' 吗？\\n\\n此操作将会：\\n• 删除所有白名单IP记录\\n• 立即使网关地址失效\\n• 此操作不可撤销！\\n\\n点击"确定"继续，点击"取消"停止操作。')) {
                return;
            }

            const confirmText = prompt('请输入"确认注销"来确认此操作：');
            if (confirmText !== '确认注销') {
                alert('确认文本不正确，操作已取消。');
                return;
            }

            const deleteBtn = document.getElementById('deleteProxyBtn');
            const originalText = showLoading(deleteBtn);

            const formData = new FormData();
            formData.append('action', 'manage_proxy');
            formData.append('sub_action', 'delete_proxy');
            formData.append('proxy_id', proxyId);
            formData.append('proxy_password', currentProxyPassword);

            try {
                const response = await fetch('/', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // 清除会话状态
                    clearSessionState();

                    document.getElementById('deleteResult').innerHTML = \`
                        <div class="result success">
                            <h3>✅ 网关注销成功！</h3>
                            <p><strong>🆔 已注销的网关ID：</strong> <code>\${result.proxy_id}</code></p>
                            <p>该网关的所有数据已被永久删除，网关地址已失效。</p>
                            <div style="margin-top: 20px; text-align: center;">
                                <button onclick="location.reload()" class="btn">🏠 返回首页</button>
                            </div>
                        </div>
                    \`;

                    // 禁用其他标签页
                    document.querySelectorAll('.tab-btn:not(.danger)').forEach(btn => {
                        btn.disabled = true;
                        btn.style.opacity = '0.5';
                    });
                } else {
                    document.getElementById('deleteResult').innerHTML = \`
                        <div class="result error">
                            <h3>❌ 注销失败</h3>
                            <p>错误详情：\${result.error || '未知错误'}</p>
                        </div>
                    \`;
                }
            } catch (error) {
                document.getElementById('deleteResult').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>错误详情：\${error.message}</p>
                    </div>
                \`;
            } finally {
                hideLoading(deleteBtn, originalText);
                // 重置确认复选框
                document.getElementById('confirmDelete').checked = false;
                deleteBtn.disabled = true;
            }
        };


    </script>
</body>
</html>`;
}

// 获取添加IP成功页面HTML
function getAddIPSuccessHTML(ip, proxyId, totalIPs, baseUrl) {
  return `<!DOCTYPE html>
<html>
<head>
    <title>IP添加成功</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            max-width: 600px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
            animation: slideUp 0.6s ease;
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px;
        }

        .info-card {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #28a745;
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            border-left: 4px solid #28a745;
        }

        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
        }

        .info-item:last-child {
            margin-bottom: 0;
        }

        .info-label {
            font-weight: 600;
            color: #155724;
            min-width: 120px;
        }

        .info-value {
            font-family: monospace;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            color: #495057;
        }

        .proxy-link {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            word-break: break-all;
            overflow-wrap: break-word;
        }

        .proxy-link a {
            color: #1976d2;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1em;
            word-break: break-all;
            overflow-wrap: break-word;
            display: inline-block;
            max-width: 100%;
            line-height: 1.4;
        }

        .proxy-link a:hover {
            text-decoration: underline;
        }

        .button-group {
            text-align: center;
            margin-top: 30px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }

        .btn.secondary:hover {
            box-shadow: 0 10px 25px rgba(108, 117, 125, 0.4);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 1.8em;
            }

            .content {
                padding: 20px;
            }

            .info-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .info-label {
                margin-bottom: 5px;
                min-width: auto;
            }

            .proxy-link {
                padding: 15px;
                margin: 15px 0;
            }

            .proxy-link a {
                font-size: 1em;
                word-break: break-all;
                line-height: 1.5;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 IP添加成功！</h1>
        </div>

        <div class="content">
            <div class="info-card">
                <div class="info-item">
                    <span class="info-label">📍 您的IP地址：</span>
                    <span class="info-value">${ip}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">🆔 网关ID：</span>
                    <span class="info-value">${proxyId}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">📊 白名单总数：</span>
                    <span class="info-value">${totalIPs} 个IP</span>
                </div>
            </div>

            <div class="proxy-link">
                <p style="margin-bottom: 10px; color: #1976d2; font-weight: 600;">🚀 您现在可以访问代理地址：</p>
                <a href="${baseUrl}/proxy/${proxyId}" target="_blank">${baseUrl}/proxy/${proxyId}</a>
            </div>

            <div class="button-group">
                <a href="/status/${proxyId}" class="btn">📋 查看白名单状态</a>
                <a href="/whitelist-helper" class="btn secondary">🛠️ 白名单助手</a>
            </div>
        </div>
    </div>
</body>
</html>`;
}

// 获取添加IP错误页面HTML
function getAddIPErrorHTML(ip, proxyId, errorMessage) {
  return `<!DOCTYPE html>
<html>
<head>
    <title>添加IP错误</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            max-width: 600px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
            animation: slideUp 0.6s ease;
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px;
        }

        .error-card {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: 1px solid #dc3545;
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            border-left: 4px solid #dc3545;
        }

        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
        }

        .info-item:last-child {
            margin-bottom: 0;
        }

        .info-label {
            font-weight: 600;
            color: #721c24;
            min-width: 120px;
        }

        .info-value {
            font-family: monospace;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            color: #495057;
        }

        .error-message {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid #dc3545;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #721c24;
            font-weight: 500;
        }

        .help-text {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #856404;
            text-align: center;
        }

        .button-group {
            text-align: center;
            margin-top: 30px;
        }

        .btn {
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn.retry {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn.retry:hover {
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.4);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }

        .btn.secondary:hover {
            box-shadow: 0 10px 25px rgba(108, 117, 125, 0.4);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 1.8em;
            }

            .content {
                padding: 20px;
            }

            .info-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .info-label {
                margin-bottom: 5px;
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>❌ 添加IP失败</h1>
        </div>

        <div class="content">
            <div class="error-card">
                <div class="info-item">
                    <span class="info-label">📍 您的IP地址：</span>
                    <span class="info-value">${ip}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">🆔 网关ID：</span>
                    <span class="info-value">${proxyId}</span>
                </div>
            </div>

            <div class="error-message">
                <strong>🚨 错误详情：</strong> ${errorMessage}
            </div>

            <div class="help-text">
                <p><strong>💡 建议解决方案：</strong></p>
                <p>• 检查网关ID是否正确</p>
                <p>• 稍后重试</p>
                <p>• 联系管理员获取帮助</p>
            </div>

            <div class="button-group">
                <a href="/api/whitelist/${proxyId}?action=add" class="btn retry">🔄 重试</a>
                <a href="/whitelist-helper" class="btn secondary">🛠️ 白名单助手</a>
            </div>
        </div>
    </div>
</body>
</html>`;
}

// 获取白名单助手页面HTML
function getWhitelistHelperHTML() {
  return `<!DOCTYPE html>
<html>
<head>
    <title>白名单助手</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px;
        }

        .input-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
            font-size: 1.1em;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: #fafbfc;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
        }

        .btn {
            color: white;
            padding: 15px 25px;
            border: none;
            border-radius: 10px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-decoration: none;
            display: inline-block;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn.primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn.primary:hover {
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.4);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .btn.secondary:hover {
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .btn.info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }

        .btn.info:hover {
            box-shadow: 0 10px 25px rgba(23, 162, 184, 0.4);
        }

        .result {
            margin-top: 25px;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .result.success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-left-color: #28a745;
            color: #155724;
        }

        .result.error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border-left-color: #dc3545;
            color: #721c24;
        }

        .result.info {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border-left-color: #17a2b8;
            color: #0c5460;
        }

        .info-section {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196f3;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
        }

        .info-section h3 {
            color: #1976d2;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .info-section p {
            margin-bottom: 10px;
            line-height: 1.6;
            color: #1976d2;
        }

        .info-section code {
            background: rgba(25, 118, 210, 0.1);
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
            color: #1565c0;
        }

        .info-section ul {
            margin-left: 20px;
            color: #1976d2;
        }

        .info-section li {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .copy-btn {
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-left: 10px;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .copy-btn:hover {
            background: #218838;
            transform: scale(1.05);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .content {
                padding: 20px;
            }

            .input-card {
                padding: 20px;
            }

            .button-group {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛠️ 白名单助手</h1>
            <p>快速添加IP到白名单或查看状态，无需登录管理界面</p>
        </div>

        <div class="content">
            <div class="input-card">
                <div class="form-group">
                    <label>🆔 网关ID</label>
                    <input type="text" id="proxyId" placeholder="请输入您的网关ID" required>
                </div>

                <div class="button-group">
                    <button onclick="addCurrentIP()" class="btn primary">➕ 将我的IP添加到白名单</button>
                    <button onclick="checkWhitelistStatus()" class="btn secondary">🔍 检查白名单状态</button>
                    <button onclick="generateWhitelistURL()" class="btn info">🔗 生成白名单链接</button>
                </div>

                <div id="result"></div>
            </div>

            <div class="info-section">
                <h3>🆕 关于新的公开服务</h3>
                <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <p><strong>📢 服务升级：</strong> 现在任何人都可以创建网关，无需管理员权限！</p>
                    <p><strong>🔐 独立管理：</strong> 每个网关都有独立的管理密码，请妥善保存。</p>
                    <p><strong>🏠 管理界面：</strong> 访问 <a href="/" style="color: #1976d2;">首页</a> 可以创建新网关或管理现有网关。</p>
                </div>

                <h3>🚀 简单方法 - 直接访问链接</h3>
                <p>您也可以通过在浏览器中访问此链接来添加您的IP：</p>
                <p><code>/api/whitelist/您的网关ID?action=add</code></p>
                <p>只需将"您的网关ID"替换为您的实际网关ID。</p>

                <h3>📖 使用说明</h3>
                <ul>
                    <li>在上方字段中输入您的网关ID</li>
                    <li>点击"生成白名单链接"获取直接链接</li>
                    <li>与用户分享此链接 - 他们只需点击即可添加自己的IP</li>
                    <li>或使用上方按钮测试功能</li>
                    <li>如需管理网关，请访问 <a href="/" style="color: #1976d2;">首页</a> 使用管理密码登录</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 复制到剪贴板功能
        function copyToClipboard(text, buttonElement) {
            navigator.clipboard.writeText(text).then(function() {
                const originalText = buttonElement.textContent;
                buttonElement.textContent = '✅ 已复制';
                buttonElement.style.background = '#28a745';
                setTimeout(() => {
                    buttonElement.textContent = originalText;
                    buttonElement.style.background = '';
                }, 2000);
            }, function(err) {
                console.error('无法复制文本: ', err);
                alert('复制失败，请手动复制');
            });
        }

        function generateWhitelistURL() {
            const proxyId = document.getElementById('proxyId').value;

            if (!proxyId) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 输入错误</h3>
                        <p>请先输入网关ID</p>
                    </div>
                \`;
                return;
            }

            const whitelistURL = \`\${window.location.origin}/api/whitelist/\${proxyId}?action=add\`;

            document.getElementById('result').innerHTML = \`
                <div class="result success">
                    <h3>🎉 白名单链接已生成！</h3>
                    <p><strong>📤 与用户分享此链接：</strong></p>
                    <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 8px; margin: 15px 0; word-break: break-all;">
                        <a href="\${whitelistURL}" target="_blank" style="color: #155724; text-decoration: none; font-weight: 600;">\${whitelistURL}</a>
                        <button onclick="copyToClipboard('\${whitelistURL}', this)" class="copy-btn">📋 复制链接</button>
                    </div>
                    <p>✨ 用户只需点击此链接即可将自己的IP添加到白名单。</p>
                </div>
            \`;
        }

        async function addCurrentIP() {
            const proxyId = document.getElementById('proxyId').value;

            if (!proxyId) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 输入错误</h3>
                        <p>请先输入网关ID</p>
                    </div>
                \`;
                return;
            }

            // 显示加载状态
            document.getElementById('result').innerHTML = \`
                <div class="result info">
                    <h3>⏳ 正在添加IP...</h3>
                    <p>请稍候，正在处理您的请求</p>
                </div>
            \`;

            try {
                const response = await fetch(\`/api/whitelist/\${proxyId}?action=add\`, {
                    method: 'GET'
                });

                // 如果返回HTML，说明成功了
                if (response.headers.get('content-type').includes('text/html')) {
                    document.getElementById('result').innerHTML = \`
                        <div class="result success">
                            <h3>🎉 添加成功！</h3>
                            <p>您的IP已成功添加到白名单！</p>
                            <p>💡 您可以直接访问代理地址或查看详细结果页面。</p>
                            <div style="margin-top: 15px;">
                                <a href="/api/whitelist/\${proxyId}?action=add" target="_blank" class="copy-btn">🔗 查看详细结果</a>
                            </div>
                        </div>
                    \`;
                } else {
                    const result = await response.json();

                    if (result.success) {
                        document.getElementById('result').innerHTML = \`
                            <div class="result success">
                                <h3>✅ 添加成功！</h3>
                                <p><strong>📝 消息：</strong> \${result.message}</p>
                                <p><strong>🌐 您的IP：</strong> <code style="background: rgba(255,255,255,0.7); padding: 2px 6px; border-radius: 4px;">\${result.ip}</code></p>
                                <p><strong>🆔 网关ID：</strong> <code style="background: rgba(255,255,255,0.7); padding: 2px 6px; border-radius: 4px;">\${result.proxy_id}</code></p>
                                <p><strong>📊 白名单总数：</strong> \${result.total_ips} 个IP</p>
                            </div>
                        \`;
                    } else {
                        document.getElementById('result').innerHTML = \`
                            <div class="result error">
                                <h3>❌ 添加失败</h3>
                                <p>错误详情：\${result.error}</p>
                                <p>💡 请检查网关ID是否正确，或稍后重试。</p>
                            </div>
                        \`;
                    }
                }
            } catch (error) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>添加IP失败：\${error.message}</p>
                        <p>💡 请检查网络连接或稍后重试。</p>
                    </div>
                \`;
            }
        }

        async function checkWhitelistStatus() {
            const proxyId = document.getElementById('proxyId').value;

            if (!proxyId) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 输入错误</h3>
                        <p>请先输入网关ID</p>
                    </div>
                \`;
                return;
            }

            // 显示加载状态
            document.getElementById('result').innerHTML = \`
                <div class="result info">
                    <h3>🔍 正在查询...</h3>
                    <p>请稍候，正在检查白名单状态</p>
                </div>
            \`;

            try {
                const response = await fetch(\`/api/whitelist/\${proxyId}\`, {
                    method: 'GET'
                });

                const result = await response.json();

                if (result.error) {
                    document.getElementById('result').innerHTML = \`
                        <div class="result error">
                            <h3>❌ 查询失败</h3>
                            <p>错误详情：\${result.error}</p>
                            <p>💡 请检查网关ID是否正确。</p>
                        </div>
                    \`;
                } else {
                    const statusIcon = result.whitelisted ? '✅' : '❌';
                    const statusText = result.whitelisted ? '已在白名单中' : '不在白名单中';
                    const statusClass = result.whitelisted ? 'success' : 'error';

                    document.getElementById('result').innerHTML = \`
                        <div class="result \${statusClass}">
                            <h3>📋 白名单状态查询结果</h3>
                            <p><strong>🔍 状态：</strong> \${statusIcon} \${statusText}</p>
                            <p><strong>🌐 您的IP：</strong> <code style="background: rgba(255,255,255,0.7); padding: 2px 6px; border-radius: 4px;">\${result.ip}</code></p>
                            <p><strong>🆔 网关ID：</strong> <code style="background: rgba(255,255,255,0.7); padding: 2px 6px; border-radius: 4px;">\${result.proxy_id}</code></p>
                            <p><strong>📊 白名单总数：</strong> \${result.total_ips} 个IP</p>
                            <div style="margin-top: 15px;">
                                <strong>📝 所有白名单IP：</strong>
                                <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 8px; margin-top: 10px; max-height: 200px; overflow-y: auto;">
                                    \${result.all_ips.length > 0 ?
                                        result.all_ips.map((ip, index) => \`
                                            <div style="padding: 8px 0; border-bottom: 1px solid rgba(0,0,0,0.1); display: flex; align-items: center;">
                                                <span style="display: inline-block; width: 6px; height: 6px; background: #007cba; border-radius: 50%; margin-right: 8px; flex-shrink: 0;"></span>
                                                <code style="background: rgba(0,124,186,0.1); padding: 2px 6px; border-radius: 4px; font-family: 'Courier New', monospace; margin-right: 8px;">\${ip}</code>
                                                \${ip === result.ip ? '<span style="color: #28a745; font-weight: bold; font-size: 12px;">✓ 您的IP</span>' : ''}
                                            </div>
                                        \`).join('')
                                        : '<div style="color: #6c757d; font-style: italic;">暂无白名单IP</div>'
                                    }
                                </div>
                            </div>
                            <div style="margin-top: 20px; text-align: center;">
                                <a href="/status/\${result.proxy_id}" target="_blank" class="copy-btn" style="background: #007bff; text-decoration: none; color: white;">
                                    🎨 查看美观的状态页面
                                </a>
                            </div>
                        </div>
                    \`;
                }
            } catch (error) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>检查状态失败：\${error.message}</p>
                        <p>💡 请检查网络连接或稍后重试。</p>
                    </div>
                \`;
            }
        }
    </script>
</body>
</html>`;
}

// 生成状态检查页面HTML
function getStatusHTML(proxyId, clientIP, isWhitelisted, totalIPs) {
  const statusIcon = isWhitelisted ? '✅' : '❌';
  const statusText = isWhitelisted ? '已在白名单中' : '不在白名单中';
  const statusColor = isWhitelisted ? '#28a745' : '#dc3545';

  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>白名单状态检查 - ${proxyId}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }

        .header {
            margin-bottom: 30px;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .status-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border-left: 5px solid ${statusColor};
        }

        .status-icon {
            font-size: 4em;
            margin-bottom: 15px;
        }

        .status-title {
            font-size: 1.8em;
            color: ${statusColor};
            font-weight: 600;
            margin-bottom: 20px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .info-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }

        .info-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-value {
            font-family: 'Courier New', monospace;
            background: white;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            word-break: break-all;
            font-size: 0.95em;
        }

        .ip-list {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
        }

        .ip-list h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .ip-item {
            background: white;
            padding: 12px 15px;
            margin: 8px 0;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-family: 'Courier New', monospace;
        }

        .ip-item.current {
            background: #d4edda;
            border-color: #c3e6cb;
            font-weight: 600;
        }

        .current-badge {
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .actions {
            margin-top: 30px;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #1e7e34;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
            transform: translateY(-2px);
        }

        .refresh-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #1565c0;
        }

        .security-notice {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .security-notice h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            .header h1 {
                font-size: 1.8em;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 白名单状态检查</h1>
            <p>实时查看您的IP白名单状态</p>
        </div>

        <div class="status-card">
            <div class="status-icon">${statusIcon}</div>
            <div class="status-title">${statusText}</div>

            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">🌐 您的IP地址</div>
                    <div class="info-value">${clientIP}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">🆔 网关ID</div>
                    <div class="info-value">${proxyId}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">📊 白名单总数</div>
                    <div class="info-value">${totalIPs} 个IP</div>
                </div>
                <div class="info-item">
                    <div class="info-label">⏰ 检查时间</div>
                    <div class="info-value" id="checkTime">${new Date().toLocaleString('zh-CN')}</div>
                </div>
            </div>
        </div>

        <div class="security-notice">
            <h3>🔒 安全提示</h3>
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; color: #856404;">
                <p><strong>📋 白名单信息：</strong></p>
                <p>• 当前白名单共有 <strong>${totalIPs}</strong> 个IP地址</p>
                <p>• 您的IP状态：${isWhitelisted ? '<span style="color: #28a745; font-weight: 600;">✅ 已在白名单中</span>' : '<span style="color: #dc3545; font-weight: 600;">❌ 不在白名单中</span>'}</p>
                <p>• 如需查看详细IP列表，请使用管理密码登录管理界面</p>
            </div>
        </div>

        <div class="refresh-info">
            💡 <strong>提示：</strong> 点击"刷新状态"可获取最新的白名单状态信息。如需查看或管理详细IP列表，请访问管理界面。
        </div>

        <div class="actions">
            <button class="btn btn-primary" onclick="refreshStatus()">
                🔄 刷新状态
            </button>
            ${!isWhitelisted ? `
            <a href="/api/whitelist/${proxyId}?action=add" class="btn btn-success">
                ➕ 添加我的IP
            </a>
            ` : ''}
            <a href="/whitelist-helper" class="btn btn-secondary">
                🛠️ 白名单工具
            </a>
            <a href="/" class="btn btn-secondary">
                🔐 管理界面
            </a>
        </div>
    </div>

    <script>
        function refreshStatus() {
            const container = document.querySelector('.container');
            container.classList.add('loading');

            // 更新检查时间
            document.getElementById('checkTime').textContent = '正在刷新...';

            // 刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 500);
        }

        // 自动刷新功能（可选）
        let autoRefresh = false;
        let refreshInterval;

        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            if (autoRefresh) {
                refreshInterval = setInterval(refreshStatus, 30000); // 30秒刷新一次
                console.log('自动刷新已启用');
            } else {
                clearInterval(refreshInterval);
                console.log('自动刷新已禁用');
            }
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
                refreshStatus();
            }
        });
    </script>
</body>
</html>`;
}

// 生成状态检查错误页面HTML
function getStatusErrorHTML(proxyId, clientIP, errorMessage) {
  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>状态检查错误 - ${proxyId}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        .error-icon {
            font-size: 4em;
            margin-bottom: 20px;
        }

        .error-title {
            color: #dc3545;
            font-size: 1.8em;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }

        .info-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: left;
        }

        .info-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }

        .info-value {
            font-family: 'Courier New', monospace;
            background: white;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            word-break: break-all;
        }

        .actions {
            margin-top: 30px;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            .actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-icon">⚠️</div>
        <div class="error-title">状态检查失败</div>

        <div class="error-message">
            <strong>错误详情：</strong> ${errorMessage}
        </div>

        <div class="info-item">
            <div class="info-label">🆔 网关ID</div>
            <div class="info-value">${proxyId}</div>
        </div>

        <div class="info-item">
            <div class="info-label">🌐 您的IP地址</div>
            <div class="info-value">${clientIP}</div>
        </div>

        <div class="actions">
            <button class="btn btn-primary" onclick="window.location.reload()">
                🔄 重试
            </button>
            <a href="/whitelist-helper" class="btn btn-secondary">
                🛠️ 白名单工具
            </a>
        </div>
    </div>
</body>
</html>`;
}

// 生成代理不存在页面HTML
function getStatusNotFoundHTML(proxyId) {
  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理未找到 - ${proxyId}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        .not-found-icon {
            font-size: 4em;
            margin-bottom: 20px;
        }

        .not-found-title {
            color: #dc3545;
            font-size: 1.8em;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .not-found-message {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #ffeaa7;
        }

        .info-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: left;
        }

        .info-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }

        .info-value {
            font-family: 'Courier New', monospace;
            background: white;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            word-break: break-all;
        }

        .actions {
            margin-top: 30px;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            .actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="not-found-icon">🔍</div>
        <div class="not-found-title">代理未找到</div>

        <div class="not-found-message">
            <strong>抱歉！</strong> 指定的网关ID不存在或已被删除。
        </div>

        <div class="info-item">
            <div class="info-label">🆔 查询的网关ID</div>
            <div class="info-value">${proxyId}</div>
        </div>

        <div class="actions">
            <a href="/" class="btn btn-primary">
                🏠 首页
            </a>
            <a href="/whitelist-helper" class="btn btn-secondary">
                🛠️ 白名单工具
            </a>
        </div>
    </div>
</body>
</html>`;
}
