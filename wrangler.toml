name = "cf-whitelist-gateway"
main = "src/index.js"
compatibility_date = "2024-01-01"

# D1 database for storing subscription URLs and IP whitelist
[[d1_databases]]
binding = "DB"
database_name = "cf-whitelist-gateway-db"
database_id = "09b8088f-beb9-4ad1-af7e-211a3361c2f1"

# KV namespace for storing subscription URLs and IP whitelist (kept for migration)
#[[kv_namespaces]]
#binding = "SUBSCRIPTION_KV"
#id = "79c7b1de05d3414baae7443bb7b4e968"

# Environment variables
[vars]
# 允许进行网关的订阅地址域名，多个域名用逗号分隔，留空表示不限制
# 例如: "example.com,api.example.com"
ALLOWED_DOMAINS = ""

# 每个网关ID的白名单IP上限数，留空表示不限制
# 例如: "100"
MAX_WHITELIST_IPS = "5"