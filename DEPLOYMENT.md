# 公开订阅代理服务部署指南

## 🚀 快速部署

### 1. 准备工作

确保您已安装：
- [Node.js](https://nodejs.org/) (推荐 LTS 版本)
- [Wrangler CLI](https://developers.cloudflare.com/workers/wrangler/install-and-update/)

```bash
npm install -g wrangler
```

### 2. 登录 Cloudflare

```bash
wrangler auth login
```

### 3. 创建 D1 数据库

```bash
# 创建 D1 数据库
wrangler d1 create cf-subscription-proxy-db
```

记录返回的数据库 ID，例如：
```
✅ Successfully created DB 'cf-subscription-proxy-db' in region APAC
Created your database using D1's new storage backend. The new storage backend is not yet recommended for production workloads, but backs up your data via point-in-time restore.

[[d1_databases]]
binding = "DB"
database_name = "cf-subscription-proxy-db"
database_id = "09b8088f-beb9-4ad1-af7e-211a3361c2f1"
```

### 4. 配置 wrangler.toml

将生成的数据库 ID 复制到 `wrangler.toml` 文件中：

```toml
name = "cf-whitelist-gateway"
main = "src/index.js"
compatibility_date = "2024-01-01"

# D1 database for storing subscription URLs and IP whitelist
[[d1_databases]]
binding = "DB"
database_name = "cf-whitelist-gateway-db"
database_id = "你的数据库ID"

# Environment variables
[vars]
# 允许进行网关的订阅地址域名，多个域名用逗号分隔，留空表示不限制
# 例如: "example.com,api.example.com"
ALLOWED_DOMAINS = ""

# 每个网关ID的白名单IP上限数，留空表示不限制
# 例如: "100"
MAX_WHITELIST_IPS = "5"
```

### 5. 初始化数据库表结构

**重要：必须先创建数据库表才能使用服务！**

```bash
# 本地开发环境 - 创建表结构
wrangler d1 execute cf-whitelist-gateway-db --local --file=./schema.sql

# 生产环境 - 创建表结构
wrangler d1 execute cf-whitelist-gateway-db --remote --file=./schema.sql
```

### 6. 部署到 Cloudflare Workers

```bash
# 开发模式（本地测试）
wrangler dev

# 部署到生产环境
wrangler deploy
```

## 🔧 配置说明

### 服务模式

这是一个**公开服务**，特点：
- ✅ 无需管理员密码
- ✅ 任何人都可以创建代理
- ✅ 每个代理有独立的管理密码
- ✅ 支持自助管理白名单

### 安全考虑

1. **资源使用**：由于是公开服务，建议监控资源使用情况
2. **存储限制**：Cloudflare D1 有存储限制，注意监控使用量
3. **访问控制**：虽然是公开服务，但每个代理的白名单仍受独立密码保护

## 📊 监控和维护

### 查看使用情况

```bash
# 查看 Workers 使用情况
wrangler metrics

# 查看 D1 数据库使用情况
wrangler d1 info cf-subscription-proxy-db

# 查看代理数量
wrangler d1 execute cf-subscription-proxy-db --remote --command="SELECT COUNT(*) as proxy_count FROM proxies;"

# 查看白名单IP总数
wrangler d1 execute cf-subscription-proxy-db --remote --command="SELECT COUNT(*) as ip_count FROM whitelist_ips;"
```

### 备份数据

```bash
# 导出网关数据
wrangler d1 execute cf-whitelist-gateway-db --remote --command="SELECT * FROM proxies;" --output=table > proxies_backup.txt

# 导出白名单数据
wrangler d1 execute cf-whitelist-gateway-db --remote --command="SELECT * FROM whitelist_ips;" --output=table > whitelist_backup.txt
```

### 清理数据

如需清理过期或无用的代理数据：

```bash
# 删除特定网关的所有数据
wrangler d1 execute cf-whitelist-gateway-db --remote --command="DELETE FROM whitelist_ips WHERE proxy_id = '网关ID';"
wrangler d1 execute cf-whitelist-gateway-db --remote --command="DELETE FROM proxies WHERE id = '网关ID';"

# 清理超过30天的网关（谨慎使用）
wrangler d1 execute cf-whitelist-gateway-db --remote --command="DELETE FROM proxies WHERE created_at < datetime('now', '-30 days');"
```

## 🌐 自定义域名（可选）

### 1. 添加自定义域名

在 Cloudflare Dashboard 中：
1. 进入 Workers & Pages
2. 选择您的 Worker
3. 点击 "Settings" > "Triggers"
4. 添加自定义域名

### 2. 更新 DNS 记录

确保您的域名指向 Cloudflare Workers：
```
your-domain.com CNAME your-worker.your-subdomain.workers.dev
```

## 🔍 故障排除

### 常见问题

1. **数据库表不存在错误**
   ```
   Error: D1_ERROR: no such table: proxies: SQLITE_ERROR
   ```
   解决：运行数据库初始化命令
   ```bash
   # 本地环境
   wrangler d1 execute cf-subscription-proxy-db --local --file=./schema.sql
   # 生产环境
   wrangler d1 execute cf-subscription-proxy-db --remote --file=./schema.sql
   ```

2. **D1 数据库错误**
   ```
   Error: D1 database not found
   ```
   解决：检查 `wrangler.toml` 中的数据库 ID 是否正确

3. **部署失败**
   ```
   Error: Authentication failed
   ```
   解决：重新运行 `wrangler auth login`

4. **代理访问失败**
   - 检查原始订阅地址是否可访问
   - 确认 IP 是否在白名单中
   - 查看 Workers 日志：`wrangler tail`

5. **创建代理时出现网络错误**
   ```
   Unexpected token '<'
   ```
   解决：通常是数据库表未初始化导致，请先运行数据库初始化命令

### 查看日志

```bash
# 实时查看 Workers 日志
wrangler tail

# 查看特定时间段的日志
wrangler tail --since 1h
```

## 📈 性能优化

### 1. 缓存策略

考虑为订阅内容添加适当的缓存：
- 静态内容：长期缓存
- 订阅内容：短期缓存（避免频繁请求源站）

### 2. 错误处理

确保所有 API 调用都有适当的错误处理和重试机制。

### 3. 监控指标

定期检查：
- 请求量和响应时间
- 错误率
- D1 数据库使用量
- Workers 执行时间

## 🔄 更新服务

### 更新代码

```bash
# 拉取最新代码
git pull origin main

# 重新部署
wrangler deploy
```

### 数据迁移

如果需要迁移现有数据，请先备份，然后使用脚本批量处理。

## 📞 支持

如遇到问题：
1. 查看 [Cloudflare Workers 文档](https://developers.cloudflare.com/workers/)
2. 检查项目的 GitHub Issues
3. 查看 Cloudflare Dashboard 中的错误日志

---

**注意**：这是一个公开服务，请确保合理使用资源，并定期监控服务状态。
