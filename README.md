# Cloudflare Workers 白名单订阅网关

这是一个基于 Cloudflare Workers 的公开订阅地址白名单网关服务，支持 IP 白名单功能。任何人都可以创建网关，每个网关都有独立的管理密码。

## 功能特性

- 🔒 **IP 白名单保护** - 只有白名单中的 IP 才能访问网关地址
- 🌐 **订阅地址网关** - 将原始订阅地址转换为网关地址
- 🆓 **公开服务** - 无需管理员权限，任何人都可以创建网关
- 🔐 **独立管理** - 每个网关都有独立的管理密码
- 🛠️ **管理界面** - 简单易用的 Web 管理界面
- ⚡ **高性能** - 基于 Cloudflare Workers 的全球边缘计算
- 🎨 **美观UI界面** - 提供现代化的状态检查页面，支持响应式设计
- 📱 **移动友好** - 完美适配手机和平板设备
- 🔄 **实时刷新** - 状态页面支持实时刷新和自动更新
- 📊 **详细信息** - 显示完整的白名单状态和IP列表
- 🗑️ **IP删除功能** - 管理界面支持删除白名单中的IP地址
- 🌍 **域名限制** - 可配置允许网关的订阅地址域名
- 📈 **数量限制** - 可配置每个网关的白名单IP上限
- 🔍 **多种查询** - 支持使用网关ID或订阅地址查询管理
- 🚫 **防重复** - 避免相同订阅地址创建多个网关

## 部署步骤

### 1. 安装依赖

```bash
npm install
```

### 2. 设置D1数据库

#### 自动设置（推荐）

```bash
# 使用自动化脚本设置D1数据库
chmod +x setup-d1.sh
./setup-d1.sh
```

#### 手动设置

```bash
# 1. 创建D1数据库
wrangler d1 create cf-whitelist-gateway-db

# 2. 更新wrangler.toml中的database_id

# 3. 初始化数据库表结构
wrangler d1 execute cf-whitelist-gateway-db --file=./schema.sql
```

### 3. 配置环境变量（可选）

在 `wrangler.toml` 文件中可以配置以下环境变量：

```toml
[vars]
# 允许进行网关的订阅地址域名，多个域名用逗号分隔，留空表示不限制
# 例如: "example.com,api.example.com"
ALLOWED_DOMAINS = ""

# 每个网关ID的白名单IP上限数，留空表示不限制
# 例如: "100"
MAX_WHITELIST_IPS = ""
```

**配置说明：**
- `ALLOWED_DOMAINS`: 限制可以创建网关的订阅地址域名，提高安全性
- `MAX_WHITELIST_IPS`: 限制每个网关的白名单IP数量，防止滥用

### 4. 部署到 Cloudflare Workers

```bash
# 开发模式
npm run dev

# 部署到生产环境
npm run deploy
```

## 使用方法

### 1. 访问服务首页

部署完成后，访问 `https://your-worker.your-subdomain.workers.dev/`

### 2. 创建新网关

1. 在首页选择 "🆕 创建新网关" 模式
2. 输入原始订阅地址（例如：`https://wd-red.com/subscribe/djkbm-mji2hi93`）
3. 点击 "创建网关"

系统会生成：
- **网关ID**：用于标识此网关
- **管理密码**：用于管理此网关的白名单（请妥善保存）
- **网关地址**：`https://your-worker.your-subdomain.workers.dev/proxy/网关ID`
- **白名单地址**：`https://your-worker.your-subdomain.workers.dev/api/whitelist/网关ID?action=add`
- **状态检查页面（UI）**：`https://your-worker.your-subdomain.workers.dev/status/网关ID`
- **状态检查地址（JSON）**：`https://your-worker.your-subdomain.workers.dev/api/whitelist/网关ID`

### 4. 管理现有网关

1. 在首页选择 "🛠️ 管理现有网关" 模式
2. 输入网关ID或完整的订阅地址
3. 输入管理密码
4. 点击 "登录管理"

**支持的查询方式：**
- 使用网关ID查询：输入创建时生成的网关ID
- 使用订阅地址查询：输入完整的原始订阅地址

登录后可以：
- 查看网关详细信息
- 查看白名单IP列表
- 添加新的IP到白名单
- 删除白名单中的IP地址

### 4.1 忘记密码？通过订阅地址找回

如果您忘记了网关密码，可以通过原始订阅地址找回：

1. 在管理页面点击 "🔍 忘记密码？通过订阅地址找回" 链接
2. 输入创建代理时使用的完整订阅地址
3. 点击 "🔍 查找密码" 按钮
4. 系统将显示对应的代理ID和管理密码

**找回成功后会显示：**
- 🆔 代理ID（带复制按钮）
- 🔐 管理密码（带复制按钮）
- 🔗 代理地址（带复制按钮）
- ✨ 白名单地址（带复制按钮）
- 🎨 状态检查页面（带复制按钮）

**注意事项：**
- 需要准确输入完整的原始订阅地址
- 只能找回通过该订阅地址创建的代理信息
- 找回后请妥善保存密码，避免再次丢失

### 5. 添加 IP 到白名单

有两种方式添加 IP 到白名单：

#### 方式一：通过管理界面
1. 使用代理ID/订阅地址和管理密码登录管理界面
2. 在 "添加IP" 标签页输入要添加的 IP 地址
3. 点击 "添加IP到白名单"

#### 方式二：通过白名单 URL（推荐给用户）
用户直接访问白名单地址，系统会自动将访问者的 IP 添加到白名单：

```bash
# 直接在浏览器中访问
https://your-worker.your-subdomain.workers.dev/api/whitelist/代理ID?action=add
```

**注意事项：**
- 如果配置了 `MAX_WHITELIST_IPS`，超过限制时会拒绝添加新IP
- 重复添加相同IP不会增加计数

### 6. 删除白名单中的IP

在管理界面中，每个白名单IP旁边都有删除按钮：

1. 登录管理界面
2. 在白名单IP列表中找到要删除的IP
3. 点击IP旁边的 🗑️ 删除按钮
4. 确认删除操作
5. 系统会自动刷新列表显示最新状态

### 7. 查看白名单状态

#### 使用状态检查UI页面（推荐）
访问 `https://your-worker.your-subdomain.workers.dev/status/代理ID`

状态检查页面提供：
- 🎨 美观的现代化界面
- 📊 当前IP的白名单状态显示
- 📝 完整的白名单IP列表
- 🔄 一键刷新功能
- ➕ 快速添加IP按钮（如果当前IP不在白名单中）
- 📱 响应式设计，支持手机和平板

#### 使用JSON接口
访问 `https://your-worker.your-subdomain.workers.dev/api/whitelist/代理ID`

返回JSON格式的状态信息，适合程序调用。

### 8. 使用代理地址

只有白名单中的 IP 才能访问代理地址。访问代理地址时，系统会：
1. 检查访问者 IP 是否在白名单中
2. 如果在白名单中，则代理请求到原始订阅地址
3. 如果不在白名单中，返回 403 错误

## API 接口

### 检查 IP 白名单状态

#### JSON 接口
```bash
GET /api/whitelist/{proxy_id}
```

返回：
```json
{
  "whitelisted": true,
  "ip": "*******",
  "proxy_id": "代理ID",
  "total_ips": 5,
  "all_ips": ["*******", "*******"]
}
```

#### UI 界面
```bash
GET /status/{proxy_id}
```

提供美观的网页界面，显示：
- 当前IP的白名单状态
- 所有白名单IP列表
- 实时刷新功能
- 快速添加IP按钮
- 响应式设计，支持移动设备

### 添加当前 IP 到白名单

```bash
GET /api/whitelist/{proxy_id}?action=add
```

返回：
```json
{
  "success": true,
  "message": "IP ******* 已成功添加到代理 xxx 的白名单",
  "ip": "*******",
  "proxy_id": "代理ID",
  "total_ips": 6
}
```

## 防重复机制

系统会自动检测重复的订阅地址：

1. **创建代理时检查**：如果订阅地址已存在，会提示错误并显示现有代理ID
2. **数据库优化**：使用D1数据库的唯一索引来防止重复订阅地址
3. **避免资源浪费**：防止同一订阅地址创建多个代理，节省存储空间
4. **管理便利性**：用户可以使用订阅地址直接查询和管理现有代理

## 技术架构

### 数据存储
- **D1数据库**：使用Cloudflare D1 SQLite数据库存储代理信息和白名单IP
- **表结构**：
  - `proxies`表：存储代理ID、订阅地址、管理密码
  - `whitelist_ips`表：存储白名单IP地址，支持外键关联
- **性能优化**：自动创建索引，支持复杂查询和关系管理



## 安全说明

- 每个代理都有独立的管理密码，请妥善保存
- 管理密码用于控制白名单，丢失后可通过订阅地址找回
- 所有数据存储在 Cloudflare D1 数据库中，具有高可用性和数据一致性
- IP 白名单检查基于 `CF-Connecting-IP` 头部，确保获取真实客户端 IP
- 公开服务意味着任何人都可以创建代理，请合理使用
- 可通过环境变量限制域名和IP数量，提高安全性
- D1数据库提供事务支持，确保数据操作的原子性

## 故障排除

1. **D1数据库错误**：确保在 `wrangler.toml` 中正确配置了 D1 数据库 ID
2. **管理密码错误**：检查代理ID和管理密码是否正确
3. **代理失败**：检查原始订阅地址是否可访问
4. **忘记管理密码**：
   - 🔍 **推荐方法**：使用密码找回功能，通过原始订阅地址找回密码
   - 📝 **备选方案**：如果也忘记了订阅地址，需要重新创建代理
5. **密码找回失败**：
   - 检查输入的订阅地址是否完整正确
   - 确认该订阅地址确实创建过代理
   - 检查网络连接和服务状态

## 许可证

MIT License
